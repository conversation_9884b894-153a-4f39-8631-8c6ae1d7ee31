/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as chat_messages from "../chat/messages.js";
import type * as chat_moderation from "../chat/moderation.js";
import type * as chat_reactions from "../chat/reactions.js";
import type * as chat_users from "../chat/users.js";
import type * as chat from "../chat.js";
import type * as internal_rooms from "../internal/rooms.js";
import type * as livekit from "../livekit.js";
import type * as moderation from "../moderation.js";
import type * as participants from "../participants.js";
import type * as rooms_core from "../rooms/core.js";
import type * as rooms_participants from "../rooms/participants.js";
import type * as rooms_recording from "../rooms/recording.js";
import type * as rooms_settings from "../rooms/settings.js";
import type * as rooms_waiting from "../rooms/waiting.js";
import type * as rooms from "../rooms.js";
import type * as streams from "../streams.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "chat/messages": typeof chat_messages;
  "chat/moderation": typeof chat_moderation;
  "chat/reactions": typeof chat_reactions;
  "chat/users": typeof chat_users;
  chat: typeof chat;
  "internal/rooms": typeof internal_rooms;
  livekit: typeof livekit;
  moderation: typeof moderation;
  participants: typeof participants;
  "rooms/core": typeof rooms_core;
  "rooms/participants": typeof rooms_participants;
  "rooms/recording": typeof rooms_recording;
  "rooms/settings": typeof rooms_settings;
  "rooms/waiting": typeof rooms_waiting;
  rooms: typeof rooms;
  streams: typeof streams;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
